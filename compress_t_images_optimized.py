#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高速图像压缩工具 - 优化版
- 递归扫描所有子文件夹中的*_T结尾图像
- 为每个文件夹创建独立压缩包
- 压缩包保存在各自文件夹的compressed子目录中
- 优化压缩速度，减少输出信息
"""

import os
import zipfile
import argparse
import time
from concurrent.futures import ThreadPoolExecutor
import multiprocessing


def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes >= 1024**3:
        return f"{size_bytes / (1024**3):.1f}GB"
    elif size_bytes >= 1024**2:
        return f"{size_bytes / (1024**2):.1f}MB"
    else:
        return f"{size_bytes / 1024:.1f}KB"


def format_time(seconds):
    """格式化时间"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    else:
        return f"{int(seconds//60)}m{seconds%60:.0f}s"


def find_t_images(directory):
    """快速扫描所有*_T结尾的图像文件"""
    extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif')
    t_images = []
    
    print("🔍 扫描文件夹...")
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('_t.jpg') or file.lower().endswith('_t.jpeg') or \
               file.lower().endswith('_t.png') or file.lower().endswith('_t.bmp') or \
               file.lower().endswith('_t.tiff') or file.lower().endswith('_t.tif') or \
               file.lower().endswith('_t.gif'):
                t_images.append(os.path.join(root, file))
    
    return t_images


def group_by_folder(images):
    """按文件夹分组"""
    groups = {}
    for img in images:
        folder = os.path.dirname(img)
        if folder not in groups:
            groups[folder] = []
        groups[folder].append(img)
    return groups


def get_file_sizes_fast(images):
    """并行计算文件大小"""
    def get_size(img):
        return img, os.path.getsize(img)
    
    file_sizes = {}
    with ThreadPoolExecutor(max_workers=multiprocessing.cpu_count()) as executor:
        results = executor.map(get_size, images)
        for img, size in results:
            file_sizes[img] = size
    
    return file_sizes


def create_compressed_folder(base_folder):
    """在基础文件夹中创建compressed子文件夹"""
    compressed_folder = os.path.join(base_folder, 'compressed')
    os.makedirs(compressed_folder, exist_ok=True)
    return compressed_folder


def compress_single_file(args):
    """压缩单个文件的工作函数"""
    img_path, archive_path, file_name = args
    try:
        with zipfile.ZipFile(archive_path, 'a', zipfile.ZIP_DEFLATED, compresslevel=1) as zf:
            zf.write(img_path, file_name)
        return True
    except Exception:
        return False


def compress_folder_fast(folder_path, images, file_sizes, max_size_gb=5):
    """快速压缩单个文件夹的图像"""
    max_size_bytes = max_size_gb * 1024 * 1024 * 1024
    folder_name = os.path.basename(folder_path)

    # 创建compressed子文件夹
    compressed_folder = create_compressed_folder(folder_path)

    # 计算总大小
    total_size = sum(file_sizes[img] for img in images)

    # 分组文件到不同的压缩包
    archives = []
    current_size = 0
    part_num = 1
    current_files = []
    all_file_groups = []

    for img in images:
        file_size = file_sizes[img]

        # 检查是否需要新建压缩包
        if current_size + file_size > max_size_bytes and current_files:
            # 确定压缩包名称
            if total_size <= max_size_bytes:
                archive_name = f"{folder_name}.zip"
            else:
                archive_name = f"{folder_name}_part{part_num:02d}.zip"

            archive_path = os.path.join(compressed_folder, archive_name)
            all_file_groups.append((archive_path, current_files))
            archives.append(archive_path)

            current_files = []
            current_size = 0
            part_num += 1

        current_files.append(img)
        current_size += file_size

    # 处理最后一组文件
    if current_files:
        if total_size <= max_size_bytes:
            archive_name = f"{folder_name}.zip"
        else:
            archive_name = f"{folder_name}_part{part_num:02d}.zip"

        archive_path = os.path.join(compressed_folder, archive_name)
        all_file_groups.append((archive_path, current_files))
        archives.append(archive_path)

    # 创建空的压缩包
    for archive_path, _ in all_file_groups:
        with zipfile.ZipFile(archive_path, 'w') as zf:
            pass  # 创建空压缩包

    # 并行添加文件
    tasks = []
    for archive_path, files in all_file_groups:
        for img in files:
            file_name = os.path.basename(img)
            tasks.append((img, archive_path, file_name))

    # 使用线程池并行处理
    with ThreadPoolExecutor(max_workers=min(4, len(tasks))) as executor:
        results = list(executor.map(compress_single_file, tasks))

    # 检查是否有失败的任务
    if not all(results):
        print(f"⚠️  部分文件压缩失败")

    return archives


def main():
    parser = argparse.ArgumentParser(description='🚀 高速图像压缩工具')
    parser.add_argument('directory', nargs='?', help='目标文件夹路径')
    parser.add_argument('--max-size', type=float, default=5.0, help='最大压缩包大小(GB)')
    
    args = parser.parse_args()
    
    # 获取目标目录
    if args.directory:
        target_dir = args.directory
    else:
        target_dir = input("📁 输入文件夹路径 (回车=当前目录): ").strip() or os.getcwd()
    
    if not os.path.exists(target_dir):
        print(f"❌ 路径不存在: {target_dir}")
        return
    
    target_dir = os.path.abspath(target_dir)
    
    print(f"📂 目标: {target_dir}")
    print(f"📦 最大大小: {args.max_size}GB")
    
    # 查找图像文件
    start_time = time.time()
    images = find_t_images(target_dir)
    
    if not images:
        print("❌ 未找到*_T结尾的图像文件")
        return
    
    print(f"✅ 找到 {len(images)} 个图像文件")
    
    # 按文件夹分组
    folder_groups = group_by_folder(images)
    print(f"📁 分布在 {len(folder_groups)} 个文件夹")
    
    # 计算文件大小
    print("📊 计算文件大小...")
    file_sizes = get_file_sizes_fast(images)
    total_size = sum(file_sizes.values())
    print(f"💾 总大小: {format_size(total_size)}")
    
    # 压缩处理
    print("🗜️  开始压缩...")
    all_archives = []
    processed = 0
    
    for folder_path, folder_images in folder_groups.items():
        folder_name = os.path.basename(folder_path)
        print(f"  📁 {folder_name} ({len(folder_images)}个文件)")
        
        archives = compress_folder_fast(folder_path, folder_images, file_sizes, args.max_size)
        all_archives.extend(archives)
        processed += len(folder_images)
        
        # 简单进度显示
        progress = processed / len(images) * 100
        print(f"     进度: {progress:.0f}%")
    
    # 结果统计
    total_time = time.time() - start_time
    compressed_size = sum(os.path.getsize(archive) for archive in all_archives if os.path.exists(archive))
    
    print(f"\n🎉 压缩完成!")
    print(f"⏱️  用时: {format_time(total_time)}")
    print(f"📦 创建: {len(all_archives)} 个压缩包")
    print(f"💾 压缩后: {format_size(compressed_size)}")
    print(f"📈 压缩率: {(1-compressed_size/total_size)*100:.1f}%")
    print(f"🚀 速度: {format_size(total_size/total_time)}/s")
    
    print(f"\n📋 压缩包位置:")
    for archive in all_archives:
        rel_path = os.path.relpath(archive, target_dir)
        print(f"  📦 {rel_path}")


if __name__ == "__main__":
    main()
