#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本功能：找到文件夹中所有*_T结尾的图像，将其压缩成zip包
- 压缩包名字是当前文件夹名
- 如果压缩包大于5G，则分开压缩，每个压缩包不超过5G
"""

import os
import zipfile
import glob
from pathlib import Path
import sys


def get_file_size(file_path):
    """获取文件大小（字节）"""
    return os.path.getsize(file_path)


def format_size(size_bytes):
    """格式化文件大小显示"""
    if size_bytes >= 1024**3:
        return f"{size_bytes / (1024**3):.2f} GB"
    elif size_bytes >= 1024**2:
        return f"{size_bytes / (1024**2):.2f} MB"
    elif size_bytes >= 1024:
        return f"{size_bytes / 1024:.2f} KB"
    else:
        return f"{size_bytes} bytes"


def find_t_images(directory):
    """查找所有以*_T结尾的图像文件"""
    # 支持常见的图像格式
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif', '*.gif']
    t_images = []
    
    for ext in image_extensions:
        # 查找以_T结尾的图像文件（不区分大小写）
        pattern_lower = f"*_t{ext}"
        pattern_upper = f"*_T{ext}"
        
        t_images.extend(glob.glob(os.path.join(directory, pattern_lower)))
        t_images.extend(glob.glob(os.path.join(directory, pattern_upper)))
    
    # 去重并排序
    t_images = sorted(list(set(t_images)))
    return t_images


def create_zip_archives(images, base_name, max_size_gb=5):
    """创建压缩包，如果超过指定大小则分割"""
    max_size_bytes = max_size_gb * 1024 * 1024 * 1024  # 转换为字节
    
    if not images:
        print("没有找到以*_T结尾的图像文件")
        return []
    
    print(f"找到 {len(images)} 个以*_T结尾的图像文件")
    
    # 计算所有文件的总大小
    total_size = sum(get_file_size(img) for img in images)
    print(f"总文件大小: {format_size(total_size)}")
    
    created_archives = []
    current_archive_index = 1
    current_size = 0
    current_zip = None
    
    try:
        for i, image_path in enumerate(images):
            file_size = get_file_size(image_path)
            file_name = os.path.basename(image_path)
            
            # 检查单个文件是否超过限制
            if file_size > max_size_bytes:
                print(f"警告: 文件 {file_name} ({format_size(file_size)}) 超过了 {max_size_gb}GB 限制，跳过此文件")
                continue
            
            # 如果当前没有打开的压缩包，或者添加当前文件会超过大小限制，则创建新的压缩包
            if current_zip is None or (current_size + file_size > max_size_bytes):
                # 关闭当前压缩包
                if current_zip is not None:
                    current_zip.close()
                    print(f"完成压缩包: {current_archive_name} ({format_size(current_size)})")
                
                # 创建新的压缩包
                if len(images) == 1 or total_size <= max_size_bytes:
                    current_archive_name = f"{base_name}.zip"
                else:
                    current_archive_name = f"{base_name}_part{current_archive_index:02d}.zip"
                
                current_zip = zipfile.ZipFile(current_archive_name, 'w', zipfile.ZIP_DEFLATED, compresslevel=6)
                created_archives.append(current_archive_name)
                current_size = 0
                current_archive_index += 1
                print(f"创建压缩包: {current_archive_name}")
            
            # 添加文件到当前压缩包
            print(f"添加文件: {file_name} ({format_size(file_size)})")
            current_zip.write(image_path, file_name)
            current_size += file_size
        
        # 关闭最后一个压缩包
        if current_zip is not None:
            current_zip.close()
            print(f"完成压缩包: {current_archive_name} ({format_size(current_size)})")
    
    except Exception as e:
        print(f"创建压缩包时出错: {e}")
        if current_zip is not None:
            current_zip.close()
        # 清理可能创建的不完整文件
        for archive in created_archives:
            if os.path.exists(archive):
                try:
                    os.remove(archive)
                    print(f"清理不完整的压缩包: {archive}")
                except:
                    pass
        return []
    
    return created_archives


def main():
    """主函数"""
    # 获取当前工作目录
    current_dir = os.getcwd()
    folder_name = os.path.basename(current_dir)
    
    print(f"当前目录: {current_dir}")
    print(f"文件夹名: {folder_name}")
    print("-" * 50)
    
    # 查找所有以*_T结尾的图像文件
    t_images = find_t_images(current_dir)
    
    if not t_images:
        print("没有找到以*_T结尾的图像文件")
        return
    
    # 创建压缩包
    archives = create_zip_archives(t_images, folder_name, max_size_gb=5)
    
    if archives:
        print("-" * 50)
        print("压缩完成！创建的压缩包:")
        for archive in archives:
            size = get_file_size(archive)
            print(f"  {archive} ({format_size(size)})")
    else:
        print("压缩失败！")


if __name__ == "__main__":
    main()
