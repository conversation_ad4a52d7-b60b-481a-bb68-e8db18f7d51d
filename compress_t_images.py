#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本功能：找到指定文件夹中所有*_T结尾的图像，将其压缩成zip包
- 压缩包名字是目标文件夹名
- 如果压缩包大于5G，则分开压缩，每个压缩包不超过5G
- 支持通过命令行参数或交互式输入指定文件夹路径
"""

import os
import zipfile
from pathlib import Path
import argparse
import time
from concurrent.futures import ThreadPoolExecutor
import multiprocessing
import lzma


def get_file_size(file_path):
    """获取文件大小（字节）"""
    return os.path.getsize(file_path)


def format_size(size_bytes):
    """格式化文件大小显示"""
    if size_bytes >= 1024**3:
        return f"{size_bytes / (1024**3):.2f} GB"
    elif size_bytes >= 1024**2:
        return f"{size_bytes / (1024**2):.2f} MB"
    elif size_bytes >= 1024:
        return f"{size_bytes / 1024:.2f} KB"
    else:
        return f"{size_bytes} bytes"


def format_time(seconds):
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        secs = seconds % 60
        return f"{int(minutes)}分{secs:.1f}秒"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{int(hours)}小时{int(minutes)}分钟"


def print_progress(current, total, prefix='进度', speed=None):
    """简化的进度显示"""
    percent = (current / total) * 100
    bar_length = 30
    filled = int(bar_length * current // total)
    bar = '█' * filled + '░' * (bar_length - filled)

    info = f'\r{prefix} [{bar}] {percent:5.1f}% ({current}/{total})'
    if speed:
        info += f' | {speed}'
    print(info, end='', flush=True)


def find_t_images(directory):
    """递归查找所有子文件夹中以*_T结尾的图像文件"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif']
    t_images = []

    print("📁 扫描文件夹...")

    # 遍历所有子文件夹
    for root, _, files in os.walk(directory):
        for file in files:
            file_lower = file.lower()
            for ext in image_extensions:
                if file_lower.endswith(f'_t{ext}'):
                    t_images.append(os.path.join(root, file))
                    break

    return sorted(list(set(t_images)))


def get_file_sizes_parallel(images):
    """并行计算文件大小以提高速度"""
    def get_size(img):
        return img, get_file_size(img)

    file_sizes = {}
    total_size = 0

    print_section("扫描文件信息")

    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(get_size, img) for img in images]

        for i, future in enumerate(futures):
            img, size = future.result()
            file_sizes[img] = size
            total_size += size

            print_progress_bar(i + 1, len(images),
                             prefix='📊 扫描文件',
                             show_speed=f"{i+1}/{len(images)} 文件")

    print()  # 换行
    return file_sizes, total_size


def group_images_by_folder(images):
    """按文件夹分组图像文件"""
    folder_groups = {}

    for image_path in images:
        folder_path = os.path.dirname(image_path)
        folder_name = os.path.basename(folder_path)

        if folder_name not in folder_groups:
            folder_groups[folder_name] = []
        folder_groups[folder_name].append(image_path)

    return folder_groups


def create_zip_archives(images, base_name, max_size_gb=5):
    """为每个文件夹创建压缩包，如果超过指定大小则分割"""
    max_size_bytes = max_size_gb * 1024 * 1024 * 1024  # 转换为字节

    if not images:
        print_error("没有找到以*_T结尾的图像文件")
        return []

    print_success(f"找到 {len(images)} 个以*_T结尾的图像文件")

    # 按文件夹分组
    folder_groups = group_images_by_folder(images)
    print_info(f"分布在 {len(folder_groups)} 个文件夹中")

    # 显示每个文件夹的文件数量
    for folder_name, folder_images in folder_groups.items():
        print_info(f"  📁 {folder_name}: {len(folder_images)} 个文件")

    # 并行计算所有文件的总大小
    file_sizes, total_size = get_file_sizes_parallel(images)

    print_info(f"总文件大小: {format_size(total_size)}")
    print_section("开始压缩")

    created_archives = []
    processed_files = 0
    processed_size = 0
    start_time = time.time()

    try:
        # 为每个文件夹创建压缩包
        for folder_name, folder_images in folder_groups.items():
            print_info(f"📁 处理文件夹: {folder_name} ({len(folder_images)} 个文件)")

            # 计算当前文件夹的总大小
            folder_size = sum(file_sizes[img] for img in folder_images)

            # 为当前文件夹创建压缩包
            current_archive_index = 1
            current_size = 0
            current_zip = None

            for image_path in folder_images:
                file_size = file_sizes[image_path]
                file_name = os.path.basename(image_path)

                # 检查单个文件是否超过限制
                if file_size > max_size_bytes:
                    print_warning(f"文件 {file_name} ({format_size(file_size)}) 超过了 {max_size_gb}GB 限制，跳过此文件")
                    processed_files += 1
                    continue

                # 如果当前没有打开的压缩包，或者添加当前文件会超过大小限制，则创建新的压缩包
                if current_zip is None or (current_size + file_size > max_size_bytes):
                    # 关闭当前压缩包
                    if current_zip is not None:
                        current_zip.close()
                        print_success(f"完成压缩包: {current_archive_name} ({format_size(current_size)})")

                    # 创建新的压缩包
                    if folder_size <= max_size_bytes:
                        current_archive_name = f"{folder_name}.zip"
                    else:
                        current_archive_name = f"{folder_name}_part{current_archive_index:02d}.zip"

                    # 使用更快的压缩设置：降低压缩级别以提高速度
                    current_zip = zipfile.ZipFile(current_archive_name, 'w', zipfile.ZIP_DEFLATED, compresslevel=1)
                    created_archives.append(current_archive_name)
                    current_size = 0
                    current_archive_index += 1
                    print_info(f"📦 创建压缩包: {current_archive_name}")

                # 添加文件到当前压缩包
                current_zip.write(image_path, file_name)

                # 更新进度
                processed_files += 1
                processed_size += file_size
                current_size += file_size

                # 计算速度和剩余时间
                elapsed_time = time.time() - start_time
                if elapsed_time > 0:
                    speed = processed_size / elapsed_time
                    remaining_size = total_size - processed_size
                    eta = remaining_size / speed if speed > 0 else 0

                    # 显示美化的进度信息
                    clear_line()
                    print_progress_bar(processed_files, len(images),
                                     prefix=f'🗜️  压缩进度',
                                     show_speed=f'{format_size(speed)}/s',
                                     show_eta=f'剩余 {format_time(eta)}')
                else:
                    clear_line()
                    print_progress_bar(processed_files, len(images),
                                     prefix=f'🗜️  压缩进度',
                                     show_speed=f'{format_size(file_size)}')

            # 关闭当前文件夹的最后一个压缩包
            if current_zip is not None:
                current_zip.close()
                print()  # 换行
                print_success(f"完成压缩包: {current_archive_name} ({format_size(current_size)})")
        
        # 显示最终统计信息
        total_time = time.time() - start_time
        print()  # 换行
        print_header("压缩完成")
        print_success(f"总用时: {format_time(total_time)}")
        print_success(f"处理文件: {processed_files}/{len(images)}")
        print_success(f"平均速度: {format_size(processed_size / total_time if total_time > 0 else 0)}/秒")
        if total_time > 0 and processed_size > 0:
            total_compressed_size = sum(get_file_size(archive) for archive in created_archives if os.path.exists(archive))
            compression_ratio = (1 - total_compressed_size / processed_size) * 100
            print_success(f"压缩率: {compression_ratio:.1f}%")
    
    except Exception as e:
        print()  # 换行
        print_error(f"创建压缩包时出错: {e}")
        if current_zip is not None:
            current_zip.close()
        # 清理可能创建的不完整文件
        for archive in created_archives:
            if os.path.exists(archive):
                try:
                    os.remove(archive)
                    print_warning(f"清理不完整的压缩包: {archive}")
                except:
                    pass
        return []
    
    return created_archives


def get_target_directory():
    """获取目标目录路径"""
    # 检查命令行参数
    parser = argparse.ArgumentParser(
        description='🗜️  高速压缩指定文件夹中所有*_T结尾的图像文件',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python compress_t_images.py                    # 交互式选择目录
  python compress_t_images.py /path/to/folder    # 指定目录
  python compress_t_images.py /path --max-size 3 # 指定目录和最大大小
        """
    )
    parser.add_argument('directory', nargs='?', help='目标文件夹路径（可选）')
    parser.add_argument('--max-size', type=float, default=5.0, help='最大压缩包大小（GB），默认5GB')
    parser.add_argument('--fast', action='store_true', help='使用快速压缩模式（压缩率较低但速度更快）')

    args = parser.parse_args()

    target_dir = None

    # 如果命令行提供了路径
    if args.directory:
        target_dir = args.directory
    else:
        # 交互式输入
        print_header("图像压缩工具")
        print_info("请输入要处理的文件夹路径:")
        print_info("（直接按回车使用当前目录）")
        user_input = input("📁 文件夹路径: ").strip()

        if user_input:
            target_dir = user_input
        else:
            target_dir = os.getcwd()

    # 验证路径
    if not os.path.exists(target_dir):
        print_error(f"路径 '{target_dir}' 不存在")
        return None, None, None

    if not os.path.isdir(target_dir):
        print_error(f"'{target_dir}' 不是一个文件夹")
        return None, None, None

    # 转换为绝对路径
    target_dir = os.path.abspath(target_dir)

    return target_dir, args.max_size, args.fast


def main():
    """主函数"""
    # 获取目标目录
    target_dir, max_size, fast_mode = get_target_directory()
    if target_dir is None:
        return

    folder_name = os.path.basename(target_dir)

    print_header("压缩任务信息")
    print_info(f"目标目录: {target_dir}")
    print_info(f"文件夹名: {folder_name}")
    print_info(f"最大压缩包大小: {max_size}GB")
    if fast_mode:
        print_info("模式: 🚀 快速压缩模式")
    else:
        print_info("模式: 🎯 标准压缩模式")

    # 查找所有以*_T结尾的图像文件
    t_images = find_t_images(target_dir)

    if not t_images:
        print_error("没有找到以*_T结尾的图像文件")
        return

    # 在当前工作目录创建压缩包
    current_dir = os.getcwd()
    os.chdir(current_dir)  # 确保压缩包创建在当前目录

    # 创建压缩包
    archives = create_zip_archives(t_images, folder_name, max_size_gb=max_size)

    if archives:
        print_header("压缩结果")
        print_success("创建的压缩包:")
        total_compressed_size = 0
        for archive in archives:
            archive_path = os.path.join(current_dir, archive)
            if os.path.exists(archive_path):
                size = get_file_size(archive_path)
                total_compressed_size += size
                print(f"  📦 {archive_path} ({format_size(size)})")
            else:
                print_warning(f"  {archive} (文件不存在)")

        print_info(f"总压缩包大小: {format_size(total_compressed_size)}")
    else:
        print_error("压缩失败！")


if __name__ == "__main__":
    main()
