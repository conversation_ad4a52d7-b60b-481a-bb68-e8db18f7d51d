#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本功能：找到指定文件夹中所有*_T结尾的图像，将其压缩成zip包
- 压缩包名字是目标文件夹名
- 如果压缩包大于5G，则分开压缩，每个压缩包不超过5G
- 支持通过命令行参数或交互式输入指定文件夹路径
"""

import os
import zipfile
import glob
from pathlib import Path
import sys
import argparse


def get_file_size(file_path):
    """获取文件大小（字节）"""
    return os.path.getsize(file_path)


def format_size(size_bytes):
    """格式化文件大小显示"""
    if size_bytes >= 1024**3:
        return f"{size_bytes / (1024**3):.2f} GB"
    elif size_bytes >= 1024**2:
        return f"{size_bytes / (1024**2):.2f} MB"
    elif size_bytes >= 1024:
        return f"{size_bytes / 1024:.2f} KB"
    else:
        return f"{size_bytes} bytes"


def find_t_images(directory):
    """查找所有以*_T结尾的图像文件"""
    # 支持常见的图像格式
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif', '*.gif']
    t_images = []
    
    for ext in image_extensions:
        # 查找以_T结尾的图像文件（不区分大小写）
        pattern_lower = f"*_t{ext}"
        pattern_upper = f"*_T{ext}"
        
        t_images.extend(glob.glob(os.path.join(directory, pattern_lower)))
        t_images.extend(glob.glob(os.path.join(directory, pattern_upper)))
    
    # 去重并排序
    t_images = sorted(list(set(t_images)))
    return t_images


def create_zip_archives(images, base_name, max_size_gb=5):
    """创建压缩包，如果超过指定大小则分割"""
    max_size_bytes = max_size_gb * 1024 * 1024 * 1024  # 转换为字节
    
    if not images:
        print("没有找到以*_T结尾的图像文件")
        return []
    
    print(f"找到 {len(images)} 个以*_T结尾的图像文件")
    
    # 计算所有文件的总大小
    total_size = sum(get_file_size(img) for img in images)
    print(f"总文件大小: {format_size(total_size)}")
    
    created_archives = []
    current_archive_index = 1
    current_size = 0
    current_zip = None
    
    try:
        for image_path in images:
            file_size = get_file_size(image_path)
            file_name = os.path.basename(image_path)
            
            # 检查单个文件是否超过限制
            if file_size > max_size_bytes:
                print(f"警告: 文件 {file_name} ({format_size(file_size)}) 超过了 {max_size_gb}GB 限制，跳过此文件")
                continue
            
            # 如果当前没有打开的压缩包，或者添加当前文件会超过大小限制，则创建新的压缩包
            if current_zip is None or (current_size + file_size > max_size_bytes):
                # 关闭当前压缩包
                if current_zip is not None:
                    current_zip.close()
                    print(f"完成压缩包: {current_archive_name} ({format_size(current_size)})")
                
                # 创建新的压缩包
                if len(images) == 1 or total_size <= max_size_bytes:
                    current_archive_name = f"{base_name}.zip"
                else:
                    current_archive_name = f"{base_name}_part{current_archive_index:02d}.zip"
                
                current_zip = zipfile.ZipFile(current_archive_name, 'w', zipfile.ZIP_DEFLATED, compresslevel=6)
                created_archives.append(current_archive_name)
                current_size = 0
                current_archive_index += 1
                print(f"创建压缩包: {current_archive_name}")
            
            # 添加文件到当前压缩包
            print(f"添加文件: {file_name} ({format_size(file_size)})")
            current_zip.write(image_path, file_name)
            current_size += file_size
        
        # 关闭最后一个压缩包
        if current_zip is not None:
            current_zip.close()
            print(f"完成压缩包: {current_archive_name} ({format_size(current_size)})")
    
    except Exception as e:
        print(f"创建压缩包时出错: {e}")
        if current_zip is not None:
            current_zip.close()
        # 清理可能创建的不完整文件
        for archive in created_archives:
            if os.path.exists(archive):
                try:
                    os.remove(archive)
                    print(f"清理不完整的压缩包: {archive}")
                except:
                    pass
        return []
    
    return created_archives


def get_target_directory():
    """获取目标目录路径"""
    # 检查命令行参数
    parser = argparse.ArgumentParser(description='压缩指定文件夹中所有*_T结尾的图像文件')
    parser.add_argument('directory', nargs='?', help='目标文件夹路径（可选）')
    parser.add_argument('--max-size', type=float, default=5.0, help='最大压缩包大小（GB），默认5GB')

    args = parser.parse_args()

    target_dir = None

    # 如果命令行提供了路径
    if args.directory:
        target_dir = args.directory
    else:
        # 交互式输入
        print("请输入要处理的文件夹路径:")
        print("（直接按回车使用当前目录）")
        user_input = input("文件夹路径: ").strip()

        if user_input:
            target_dir = user_input
        else:
            target_dir = os.getcwd()

    # 验证路径
    if not os.path.exists(target_dir):
        print(f"错误: 路径 '{target_dir}' 不存在")
        return None, None

    if not os.path.isdir(target_dir):
        print(f"错误: '{target_dir}' 不是一个文件夹")
        return None, None

    # 转换为绝对路径
    target_dir = os.path.abspath(target_dir)

    return target_dir, args.max_size


def main():
    """主函数"""
    # 获取目标目录
    target_dir, max_size = get_target_directory()
    if target_dir is None:
        return

    folder_name = os.path.basename(target_dir)

    print(f"目标目录: {target_dir}")
    print(f"文件夹名: {folder_name}")
    print(f"最大压缩包大小: {max_size}GB")
    print("-" * 50)

    # 查找所有以*_T结尾的图像文件
    t_images = find_t_images(target_dir)

    if not t_images:
        print("没有找到以*_T结尾的图像文件")
        return

    # 在当前工作目录创建压缩包
    current_dir = os.getcwd()
    os.chdir(current_dir)  # 确保压缩包创建在当前目录

    # 创建压缩包
    archives = create_zip_archives(t_images, folder_name, max_size_gb=max_size)

    if archives:
        print("-" * 50)
        print("压缩完成！创建的压缩包:")
        for archive in archives:
            archive_path = os.path.join(current_dir, archive)
            if os.path.exists(archive_path):
                size = get_file_size(archive_path)
                print(f"  {archive_path} ({format_size(size)})")
            else:
                print(f"  {archive} (文件不存在)")
    else:
        print("压缩失败！")


if __name__ == "__main__":
    main()
